<template>
  <!--
    组件：付款审批-财务审批页面
    路径：src\views\work\process\mach\flowContract\flowApvContractPayInfo\flowApvContractPayInfoFinanceAudit.vue
  -->
  <basic-container>
    <el-tabs v-model="flowTabSelected">
      <el-tab-pane label="流程信息" name="flow">
        <el-row>
          <el-col :span="24">
            <histoicFlowVue :processInstanceId="processInstanceId" :taskId="taskId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="流程图" name="diagram">
        <el-row>
          <el-col :span="24">
            <flowDiagramVue :processInstanceId="processInstanceId" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="表单信息" name="form">
        <el-row>
          <el-col :span="24">
            <mach-card title="付款审批单">
              <template #extra>
                <el-button type="primary" icon="el-icon-printer" @click="handlePrint">打印</el-button>
              </template>
              <avue-form
                :option="payinfoOption"
                v-model="form.flowApvContractPayInfo"
                ref="payInfoForm"
                disabled
              >
                <!-- 隐藏avue-form自带的操作按钮 -->
                <template #menu-form="{}">
                  <!-- 不显示任何内容，隐藏清空和提交按钮 -->
                </template>
              </avue-form>

              <el-divider content-position="left">支付方式</el-divider>

              <el-row style="margin:0 auto 10px auto;" >
                <div style="margin-bottom:10px;">
                  <el-button
                        type="primary"
                        icon="Plus"
                        @click="handleAddPayment"
                    >添加支付方式
                    </el-button>
                </div>
                <div style="border:1px solid #d9ecff;border-radius: 5px;background:#ecf5ff;width:100%;padding:5px 10px;">
                  <span style="font-size:14px;">付款金额：</span>
                  <el-tag type="danger">￥{{ formatAmount(form.flowApvContractPayInfo.amount) }}</el-tag>
                  <span style="font-size:14px;margin-left:20px;">支付方式金额：</span>
                  <el-tag :type="payTypeTotalAmount === form.flowApvContractPayInfo.amount ? 'success' : 'danger'">
                    ￥{{ formatAmount(payTypeTotalAmount) }}
                  </el-tag>
                  <span v-if="payTypeTotalAmount !== form.flowApvContractPayInfo.amount" style="color: #f56c6c; margin-left: 10px;">
                    <el-icon><Warning /></el-icon>
                    金额不一致，请调整支付方式
                  </span>
                </div>
              </el-row>

              <avue-crud
                :option="paymentOption"
                :table-loading="loading"
                :data="form.flowApvContractPayInfo.payDetailList"
                ref="crud"
                @row-update="rowUpdate"
                @row-save="rowSave"
                @row-del="rowDel"
              >
                <!-- 隐藏avue-crud自带的操作按钮 -->
                <template #menu-form="{}">
                  <!-- 不显示任何内容，隐藏清空和提交按钮 -->
                </template>
                <!-- 列表：支付金额 -->
                <template #amount="{row}">
                  <span>￥{{ formatAmount(row.amount) }}</span>
                </template>
              </avue-crud>
            </mach-card>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="PDF预览" name="pdf">
        <el-row>
          <el-col :span="24">
            <div v-if="pdfLoading" style="text-align: center; padding: 50px;">
              <el-icon class="is-loading"><Loading /></el-icon>
              <p>正在生成PDF预览...</p>
            </div>
            <div v-else-if="pdfError" style="text-align: center; padding: 50px;">
              <el-icon><Warning /></el-icon>
              <p>PDF预览加载失败</p>
            </div>
            <machPdf v-else-if="pdfSrc" :src="pdfSrc" />
            <div v-else style="text-align: center; padding: 50px;">
              <el-icon><Document /></el-icon>
              <p>暂无PDF预览</p>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <el-divider content-position="left">审批意见</el-divider>

    <avue-form :option="auditOption" v-model="auditForm" ref="auditFormRef">
      <!-- 隐藏avue-form自带的操作按钮 -->
      <template #menu-form="{}">
        <!-- 不显示任何内容，隐藏清空和提交按钮 -->
      </template>
    </avue-form>

    <el-form-item style="margin-bottom: 0;">
      <el-button type="primary" icon="Check" @click="handleSubmit" :loading="submitLoading">提交</el-button>
      <el-button icon="Close" @click="handleCancel">关闭</el-button>
    </el-form-item>
  </basic-container>
</template>

<script setup>
  import { ref, getCurrentInstance, computed, onMounted } from 'vue';
  import { Loading, Warning, Document } from '@element-plus/icons-vue';
  import histoicFlowVue from '@/views/work/process/mach/histoicFlow/histoicFlow.vue';
  import flowDiagramVue from '@/views/work/process/mach/flow-diagram/flow-diagram.vue';
  import mach from "@/utils/mach";
  import machPdf from "@/components/mach/pdf/mach-pdf.vue";
  import MachCard from "@/components/mach/card/mach-card.vue";
  import { auditFormDetail, audit } from "@/api/mach/pm/flowApvContractPayInfo/apvContractPayInfo";
  import { moneyInterval } from "@/utils/util";

  // 获取this
  let { proxy } = getCurrentInstance()

  // 属性
  const props = defineProps({
    taskId:{},
    processInstanceId:{},
    businessId:{},
    processDefinitionId:{},
    taskDefinitionKey:{},
    status:{},
    taskName:{},
    param:{},
  });



  const flowTabSelected = ref("flow");
  const submitLoading = ref(false);
  const loading = ref(false);
  const pdfLoading = ref(false);
  const pdfError = ref(false);
  const pdfSrc = ref("");
  const auditFormRef = ref(null);
  const form = ref({
    flowApvContractPayInfo: {
      payDetailList: []
    }
  });
  const auditOption = ref({});
  const auditForm = ref({
    comment: "",
    flag: "1",
    backTaskKey: ""
  });

  // 付款单-表单
  const payinfoOption = ref({
    submitBtn: false,  // 隐藏提交按钮
    emptyBtn: false,   // 隐藏清空按钮
    labelWidth: 120,   // 标签宽度
    detail: true,      // 详情模式
    column: [
      {
        label: "基本信息",
        prop: "baseInfo",
        icon: "el-icon-info",
        collapse: false,  // 默认展开，不显示箭头
        column: [
          {
            label: "编号",
            prop: "code",
            span: 8
          },
          {
            label: "甲方",
            prop: "partyA",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "北京",
                value: "3"
              },
              {
                label: "江苏",
                value: "2"
              },
              {
                label: "天津",
                value: "1"
              }
            ]
          },
          {
            label: "收款单位",
            prop: "payee",
            span: 8
          },
          {
            label: "单位简称",
            prop: "payeeSimple",
            span: 8
          },
          {
            label: "紧急程度",
            prop: "level",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "一般",
                value: "1"
              },
              {
                label: "紧急",
                value: "2"
              },
              {
                label: "特急",
                value: "3"
              }
            ]
          },
          {
            label: "款项用途",
            prop: "fundsUse",
            span: 8
          },
          {
            label: "支出说明",
            prop: "payDesc",
            span: 24
          },
          {
            label: "付款金额",
            prop: "amount",
            span: 8,
            formatter: (val) => {
              return "￥" + formatAmount(val);
            }
          }
        ]
      },
      {
        label: "银行信息",
        prop: "bankInfo",
        icon: "el-icon-bank-card",
        collapse: false,  // 默认展开，不显示箭头
        column: [
          {
            label: "银行信息是否变更",
            prop: "bankChange",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "开户行",
            prop: "bankAddress",
            span: 8
          },
          {
            label: "账号",
            prop: "bankAccount",
            span: 8
          }
        ]
      },
      {
        label: "其他信息",
        prop: "otherInfo",
        icon: "el-icon-document",
        collapse: false,  // 默认展开，不显示箭头
        column: [
          {
            label: "是否开具发票",
            prop: "invoiceStatus",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "发票备注",
            prop: "invoiceRemark",
            span: 16
          },
          {
            label: "货物是否入库",
            prop: "instoreStatus",
            type: "select",
            span: 8,
            dicData: [
              {
                label: "否",
                value: "0"
              },
              {
                label: "是",
                value: "1"
              }
            ]
          },
          {
            label: "入库备注",
            prop: "instoreRemark",
            span: 16
          },
          {
            label: "支付方式说明",
            prop: "paytypeDesc",
            span: 24
          },
          {
            label: "备注信息",
            prop: "remarks",
            span: 24
          }
        ]
      }
    ]
  });

  // 支付方式-列表
  const paymentOption = ref({
    height: 'auto',
    calcHeight: 30,
    searchShow: false,
    border: true,
    index: true,
    selection: false,
    viewBtn: false,
    editBtn: true,
    delBtn: true,
    addBtn: false,
    menu: true,
    menuWidth: 160,
    column: [
      {
        label: "支付方式",
        prop: "payType",
        type: "select",
        dicData: [
          {
            label: "现金",
            value: "1"
          },
          {
            label: "银行转账",
            value: "2"
          },
          {
            label: "支票",
            value: "3"
          }
        ],
        rules: [
          {
            required: true,
            message: "请选择支付方式",
            trigger: "blur"
          }
        ]
      },
      {
        label: "金额",
        prop: "amount",
        type: "number",
        precision: 2,
        rules: [
          {
            required: true,
            message: "请输入金额",
            trigger: "blur"
          }
        ]
      },
      {
        label: "备注",
        prop: "remark"
      }
    ]
  });

  // 计算支付方式总金额
  const payTypeTotalAmount = computed(() => {
    let totalAmount = 0;
    if (form.value.flowApvContractPayInfo && form.value.flowApvContractPayInfo.payDetailList) {
      form.value.flowApvContractPayInfo.payDetailList.forEach(item => {
        totalAmount = mach.accAdd(totalAmount, item.amount || 0);
      });
    }
    return totalAmount;
  });

  function formatAmount(a){
    return moneyInterval(a);
  }

  // 打印
  function handlePrint() {
    window.print();
  }

  // 添加支付方式
  function handleAddPayment() {
    proxy.$refs.crud.rowAdd();
  }

  // 保存支付方式
  function rowSave(row, done) {
    if (!form.value.flowApvContractPayInfo.payDetailList) {
      form.value.flowApvContractPayInfo.payDetailList = [];
    }
    form.value.flowApvContractPayInfo.payDetailList.push(row);
    done();
  }

  // 更新支付方式
  function rowUpdate(row, index, done) {
    form.value.flowApvContractPayInfo.payDetailList[index] = row;
    done();
  }

  // 删除支付方式
  function rowDel(row, index) {
    form.value.flowApvContractPayInfo.payDetailList.splice(index, 1);
  }

  // 生成PDF预览
  function generatePdfPreview() {
    if (!form.value.flowApvContractPayInfo || !form.value.flowApvContractPayInfo.id) {
      pdfError.value = true;
      return;
    }

    pdfLoading.value = true;
    pdfError.value = false;

    // 构建PDF预览URL - 修复：使用正确的接口名称
    const pdfUrl = `/blade-apvContractPayInfo/apvContractPayInfo/viewApvPdfOutStream?id=${form.value.flowApvContractPayInfo.id}`;
    pdfSrc.value = pdfUrl;

    // 模拟加载完成
    setTimeout(() => {
      pdfLoading.value = false;
    }, 1000);
  }

  // 审核提交
  async function handleSubmit() {
    auditFormRef.value.clearValidate();

    // 审批意见校验
    let formValidRes = await new Promise((resolve) =>{
        auditFormRef.value.validate((valid, done)=>{
            resolve(valid);
            done();
        })
    });

    if(formValidRes == false){
        proxy.$Message.warning("请完成校验项");
        return false;
    }

    // 检查支付方式总金额是否等于付款金额
    if (payTypeTotalAmount.value != form.value.flowApvContractPayInfo.amount) {
      proxy.$Message.warning("支付方式总金额与付款金额不一致");
      return false;
    }

    let params = Object.assign({}, form.value.flowApvContractPayInfo, {
        flow: {
            taskId: props.taskId,
            processInstanceId: props.processInstanceId,
            businessId: props.businessId,
            processDefinitionId: props.processDefinitionId,
            taskDefinitionKey: props.taskDefinitionKey,
            status: props.status,
            flag: auditForm.value.flag,
            comment: auditForm.value.comment,
        },
        backTaskKey: auditForm.value.backTaskKey,
    });

    submitLoading.value = true;
    audit(params).then(resp => {
      submitLoading.value = false;
      proxy.$Message({message: resp.data.msg, type: "success"});
      handleCancel();
    }).catch(() => {
      submitLoading.value = false;
      proxy.$Message({message: "提交异常，请联系管理员", type: "error"});
    });
  }

  // 关闭页面
  function handleCancel() {
    proxy.$router.$avueRouter.closeTag();
    proxy.$router.push({ path: `/work/tasks` });
  }

  function init() {
    // 获取审批表单配置
    auditOption.value = {
      submitBtn: false,  // 隐藏提交按钮
      emptyBtn: false,   // 隐藏清空按钮
      labelWidth: 120,   // 标签宽度
      column: [
        {
          label: "审批结果",
          prop: "flag",
          type: "radio",
          dicData: [
            {
              label: "同意",
              value: "1"
            },
            {
              label: "驳回",
              value: "0"
            }
          ],
          rules: [
            {
              required: true,
              message: "请选择审批结果",
              trigger: "blur"
            }
          ],
          change: (val) => {
            let backTaskKeyOpt = proxy.findObject(auditOption.value.column, "backTaskKey");
            if (backTaskKeyOpt) {
              backTaskKeyOpt.display = val === "0";
            }
          }
        },
        {
          label: "驳回节点",
          prop: "backTaskKey",
          type: "select",
          display: false,
          dicData: [
            {
              label: "发起人",
              value: "apply_edit"
            }
          ],
          rules: [
            {
              required: true,
              message: "请选择驳回节点",
              trigger: "blur"
            }
          ]
        },
        {
          label: "审批意见",
          prop: "comment",
          type: "textarea",
          span: 24,
          minRows: 3,
          maxRows: 5,
          rules: [
            {
              required: true,
              message: "请输入审批意见",
              trigger: "blur"
            }
          ]
        }
      ]
    };

    // 获取表单数据
    let flow = {
      taskId: props.taskId,
      processInstanceId: props.processInstanceId,
      businessId: props.businessId
    };

    auditFormDetail(flow).then(resp => {
      form.value = resp.data.data;

      // 如果没有支付方式，添加一个默认的
      if (!form.value.flowApvContractPayInfo.payDetailList || form.value.flowApvContractPayInfo.payDetailList.length === 0) {
        form.value.flowApvContractPayInfo.payDetailList = [{
          payType: "2", // 默认银行转账
          amount: form.value.flowApvContractPayInfo.amount,
          remark: ""
        }];
      }

      // 生成PDF预览
      generatePdfPreview();
    });
  }

  onMounted(() => {
    init();
  });
</script>

<style lang="scss" scoped>
</style>
